<div align="center"><a name="readme-top"></a>

<img src="./imgs/logo.png" width="120" height="120" alt="autoMate logo">
<h1>autoMate</h1>
<p><b>🤖 AI駆動のローカル自動化ツール | コンピュータに仕事を任せる</b></p>

[English](./README.md) | [中文](./README_CN.md)

>"面倒な作業を自動化し、時間を生活に取り戻す"

https://github.com/user-attachments/assets/bf27f8bd-136b-402e-bc7d-994b99bcc368


</div>

> **特別声明：** autoMateプロジェクトは現在も急速な開発段階にあり、最新の技術を継続的に探索・統合しています。この過程で、**より深い設計思考、技術スタックの議論、直面する課題と解決策、およびAI+RPA分野に関する継続的な研究ノートは、主に[知識プラネット「AI桐木とその仲間たち」](https://t.zsxq.com/x1cCW)**で共有・議論されています。
>
> autoMateの技術的な詳細、開発方向性、またはより広範なAI自動化トピックに興味がある方は、QRコードをスキャンして参加し、私や他の仲間たちと一緒にautoMateの成長を目撃しましょう！

<div align="center">
<figure>
    <a href="[あなたの知識プラネットリンク]" target="_blank" rel="noopener noreferrer"><img src="./imgs/knowledge.png" width="150" height="150" alt="知識プラネットQRコード"></a>
</figure>
</div>


## 💫 コンピュータとの関係を再定義

従来のRPAツールとは異なり、autoMateは大規模言語モデルの力を活用し、自然言語でタスクを説明するだけで複雑な自動化プロセスを完了します。繰り返し作業にさようならし、本当に価値を生み出すことに集中しましょう！

**自動化で生活により多くの可能性を。**

## 💡 プロジェクト概要
autoMateは、OmniParserをベースにした革新的なAI+RPA自動化ツールで、以下のことができます：

- 📊 要件を理解し、自動的にタスクを計画
- 🔍 画面の内容をインテリジェントに理解し、人間の視覚と操作をシミュレート
- 🧠 自律的な判断を行い、タスク要件に基づいて判断と行動を実行
- 💻 ローカルデプロイメントをサポートし、データセキュリティとプライバシーを保護

## ✨ 主な機能

- 🔮 ノーコード自動化 - 自然言語でタスクを記述、プログラミング知識不要
- 🖥️ 全インターフェース制御 - 特定のソフトウェアに限定されない、あらゆる視覚的インターフェースの操作をサポート
- 🚅 簡単なインストール - 中国語環境をサポート、ワンクリックデプロイ

## 🚀 クイックスタート

### 📥 直接使用
GitHubリリースから実行ファイルを直接ダウンロードできます。

### 📦 インストール
まずminiCondaのインストールを強く推奨します。minicondaで依存関係をインストールしてください。オンラインに多くのチュートリアルがありますが、わからない場合はAIに質問することもできます。その後、以下のコマンドで環境をセットアップします：

```bash
# プロジェクトをクローン
git clone https://github.com/yuruotong1/autoMate.git
cd autoMate
# Python 3.12環境を作成
conda create -n "automate" python==3.12
# 環境をアクティベート
conda activate automate
# 依存関係をインストール
python install.py
```

インストール後、コマンドラインでアプリケーションを起動できます：

```bash
python main.py
```

その後、ブラウザで`http://localhost:7888/`を開き、APIキーと基本設定を構成してください。

### 🔔 注意

現在テスト済みでサポートされているモデルは以下の通りです：

> PS：以下はテスト済みで動作する大規模モデルベンダーです。これらのベンダーとは関係がないため、アフターサービス、機能保証、安定性維持は保証しません。支払い状況を慎重にご検討ください。

| ベンダー | モデル |
| --- | --- |
|[yeka](https://2233.ai/api)|gpt-4o,o1|
|openai|gpt-4o,gpt-4o-2024-08-06,gpt-4o-2024-11-20,o1,4.gpt-4.5-preview-2025-02-27|

## 📝 よくある質問
### どのモデルがサポートされていますか？
現在はOpenAIシリーズのモデルのみをサポートしています。中国でOpenAIにアクセスできない場合は、[yeka](https://2233.ai/api)をプロキシとして使用することをお勧めします。

他のモデルをサポートしない理由：マルチモーダル+構造化出力機能を使用しているため、他のモデルベンダーで両方の機能を同時にサポートしているところはほとんどありません。他のモデルに適応するには、アーキテクチャの大幅な変更が必要で、結果も保証できません。ただし、解決策を積極的に探しており、利用可能になり次第すぐに更新します。

### 実行速度が遅いのはなぜですか？
NVIDIAの専用グラフィックスカードがない場合、実行速度が遅くなります。これは、視覚的な注釈のためにOCRを頻繁に呼び出し、大量のGPUリソースを消費するためです。私たちは積極的に最適化と適応を行っています。少なくとも4GBのVRAMを持つNVIDIAグラフィックスカードの使用を推奨し、バージョンはtorchバージョンと一致している必要があります：

1. `pip list`を実行してtorchバージョンを確認
2. [公式サイト](https://pytorch.org/get-started/locally/)でサポートされているcudaバージョンを確認
3. インストールされているtorchとtorchvisionをアンインストール
4. 公式のtorchインストールコマンドをコピーし、お使いのcudaバージョンに適したtorchを再インストール

例えば、cudaバージョンが12.4の場合、以下のコマンドでtorchをインストールする必要があります：

```bash
pip3 uninstall -y torch torchvision
pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124
```

## 🤝 参加する

優れたオープンソースプロジェクトは、集団の知恵の結晶です。autoMateの成長は、あなたの参加と貢献なしには成り立ちません。バグ修正、機能追加、ドキュメント改善など、あなたの貢献は何千人もの人々が繰り返し作業から解放されるのを助けます。

よりインテリジェントな未来の創造に参加しましょう。

<a href="https://github.com/yuruotong1/autoMate/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=yuruotong1/autoMate" />
</a>

---

<div align="center">
⭐ スターは制作者への励ましであり、より多くの人々がautoMateを発見し恩恵を受ける機会です ⭐
今日のあなたのサポートが、明日の私たちの進歩の原動力です
</div>
